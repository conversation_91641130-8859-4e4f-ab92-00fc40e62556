#!/usr/bin/env python3
"""
独立MCP客户端示例
演示如何不依赖MCPManager直接调用MCP工具
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any, Optional, List

# 添加项目路径到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.client.stdio import stdio_client
from mcp import StdioServerParameters
from common.logger.logger import logger


class StandaloneMCPClient:
    """
    独立MCP客户端
    
    支持直接连接MCP服务器并调用工具，无需MCPManager
    """
    
    def __init__(self, server_url: str, protocol: str = "sse", **kwargs):
        """
        初始化独立MCP客户端
        
        Args:
            server_url: 服务器地址 (SSE) 或命令 (Stdio)
            protocol: 协议类型 ("sse" | "stdio")
            **kwargs: 额外配置参数
        """
        self.server_url = server_url
        self.protocol = protocol
        self.config = kwargs
        self.session = None
        self.context = None
        
    async def connect(self):
        """建立连接"""
        if self.protocol == "sse":
            await self._connect_sse()
        elif self.protocol == "stdio":
            await self._connect_stdio()
        else:
            raise ValueError(f"不支持的协议: {self.protocol}")
    
    async def _connect_sse(self):
        """建立SSE连接"""
        timeout = self.config.get("timeout", 10.0)
        sse_read_timeout = self.config.get("sse_read_timeout", 300.0)
        
        self.context = sse_client(self.server_url, timeout=timeout, sse_read_timeout=sse_read_timeout)
        read, write = await self.context.__aenter__()
        
        self.session = ClientSession(read, write)
        await self.session.__aenter__()
        await self.session.initialize()
        
        logger.info(f"SSE连接建立成功: {self.server_url}")
    
    async def _connect_stdio(self):
        """建立Stdio连接"""
        params = StdioServerParameters(
            command=self.config.get("command", "python3"),
            args=self.config.get("args", []),
            env=self.config.get("env", {})
        )
        
        self.context = stdio_client(params)
        read, write = await self.context.__aenter__()
        
        self.session = ClientSession(read, write)
        await self.session.__aenter__()
        await self.session.initialize()
        
        logger.info("Stdio连接建立成功")
    
    async def list_tools(self) -> List[str]:
        """获取可用工具列表"""
        if not self.session:
            await self.connect()
        
        tools_result = await self.session.list_tools()
        return [tool.name for tool in tools_result.tools]
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """调用工具"""
        if not self.session:
            await self.connect()
        
        return await self.session.call_tool(tool_name, arguments=arguments)
    
    async def close(self):
        """关闭连接"""
        if self.session:
            await self.session.__aexit__(None, None, None)
        if self.context:
            await self.context.__aexit__(None, None, None)
        
        logger.info("连接已关闭")
    
    async def __aenter__(self):
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()


async def demo_jupyter_client():
    """演示Jupyter客户端调用"""
    print("🐍 === Jupyter 代码执行演示 ===")
    
    # 方式1: 使用SSE协议连接远程服务器
    async with StandaloneMCPClient("http://localhost:8900/sse", "sse") as client:
        # 获取工具列表
        tools = await client.list_tools()
        print(f"可用工具: {tools}")
        
        # 执行简单代码
        result1 = await client.call_tool(
            "execute_code",
            {
                "code": """
print("=== MCP独立客户端测试 ===")
import datetime
print(f"当前时间: {datetime.datetime.now()}")

# 简单计算
numbers = [1, 2, 3, 4, 5]
total = sum(numbers)
average = total / len(numbers)

print(f"数字列表: {numbers}")
print(f"总和: {total}")
print(f"平均值: {average}")
                """
            }
        )
        print("执行结果1:", result1)
        
        # 执行数据分析代码
        result2 = await client.call_tool(
            "execute_code",
            {
                "code": """
import pandas as pd
import numpy as np

# 创建示例销售数据
np.random.seed(42)
data = {
    'product': ['iPhone', 'iPad', 'MacBook', 'AirPods', 'Apple Watch'] * 20,
    'sales_amount': np.random.randint(1000, 10000, 100),
    'region': np.random.choice(['北京', '上海', '广州', '深圳'], 100),
    'month': np.random.choice(['2024-01', '2024-02', '2024-03'], 100)
}

df = pd.DataFrame(data)

print("销售数据概览:")
print(df.head())
print(f"\\n数据形状: {df.shape}")

# 按产品分组统计
product_stats = df.groupby('product')['sales_amount'].agg(['sum', 'mean', 'count'])
print("\\n按产品统计:")
print(product_stats)

# 按地区分组统计
region_stats = df.groupby('region')['sales_amount'].sum().sort_values(ascending=False)
print("\\n按地区销售额排序:")
print(region_stats)
                """,
                "required_packages": ["pandas", "numpy"]
            }
        )
        print("执行结果2:", result2)


async def demo_sql_generation():
    """演示SQL生成工具调用"""
    print("\n🗄️ === SQL 生成演示 ===")
    
    async with StandaloneMCPClient("http://localhost:8901/sse", "sse") as client:
        # 生成简单查询SQL
        result1 = await client.call_tool(
            "generate_sql",
            {
                "engine_type": "dlc",
                "params": {
                    "question": "查询销售额最高的前10个产品",
                    "app_id": "demo_app",
                    "sub_account_uin": "demo_user",
                    "trace_id": "demo_trace_001",
                    "data_engine_name": "demo_engine",
                    "db_info": '[{"CatalogName": "SalesCatalog", "DbName": "sales_db", "TableList": ["products", "sales"]}]',
                    "is_sampling": False,
                    "mcp_url": {"DLC": "http://localhost:8080/sse"},
                    "type": "DLC",
                    "record_id": "demo_001"
                }
            }
        )
        
        print("生成的SQL:")
        print(result1.get("sql", ""))
        print("\n推理过程:")
        print(result1.get("reasoning", ""))
        
        # 生成复杂查询SQL
        result2 = await client.call_tool(
            "generate_sql",
            {
                "engine_type": "dlc",
                "params": {
                    "question": "分析每个地区每个月的销售趋势，计算同比增长率",
                    "app_id": "demo_app",
                    "sub_account_uin": "demo_user",
                    "trace_id": "demo_trace_002",
                    "data_engine_name": "demo_engine",
                    "db_info": '[{"CatalogName": "SalesCatalog", "DbName": "sales_db", "TableList": ["sales", "regions", "time_dim"]}]',
                    "is_sampling": False,
                    "mcp_url": {"DLC": "http://localhost:8080/sse"},
                    "type": "DLC",
                    "record_id": "demo_002"
                }
            }
        )
        
        print("\n复杂查询SQL:")
        print(result2.get("sql", ""))


async def demo_code_generation():
    """演示代码生成工具调用"""
    print("\n💻 === 代码生成演示 ===")
    
    async with StandaloneMCPClient("http://localhost:8902/sse", "sse") as client:
        # 生成数据分析代码
        result = await client.call_tool(
            "nl2code",
            {
                "params": {
                    "scenario": "correlation_analysis",
                    "user_instruction": "分析销售数据中产品价格、销售量、地区之间的相关性，生成相关性矩阵和可视化图表",
                    "env_dependencies": ["pandas", "numpy", "matplotlib", "seaborn"],
                    "global_vars": {"sales_df": "销售数据DataFrame"},
                    "function_headers": [],
                    "previous_actions": [],
                    "data_type": "DataFrame",
                    "data_schema": "columns: [product_id, product_name, price, sales_quantity, region, sales_date, revenue]",
                    "model_name": "DeepSeek-V3-0324"
                }
            }
        )
        
        print("生成的Python代码:")
        print("=" * 60)
        print(result.get("python_code", ""))
        print("=" * 60)
        print(f"\n所需包: {result.get('required_packages', [])}")
        print(f"检测场景: {result.get('detected_scenario', '')}")


async def demo_ai_search():
    """演示AI搜索工具调用"""
    print("\n🔍 === AI 搜索演示 ===")
    
    async with StandaloneMCPClient("http://localhost:8903/sse", "sse") as client:
        # 搜索数据科学相关文档
        result = await client.call_tool(
            "aisearch_retrieve",
            {
                "question": "如何使用Python进行时间序列分析和预测？",
                "recall_num": 3
            }
        )
        
        print("搜索结果:")
        for i, doc in enumerate(result.get("aisearch", []), 1):
            print(f"\n📄 文档 {i}:")
            print(f"内容: {doc['content'][:150]}...")
            print(f"相关性评分: {doc['score']:.3f}")
            print(f"重排序评分: {doc['rerank_score']:.3f}")


async def demo_stdio_local_client():
    """演示本地Stdio客户端"""
    print("\n🖥️ === 本地 Stdio 客户端演示 ===")
    
    # 配置本地Jupyter服务器
    config = {
        "command": "python3",
        "args": ["infra/mcp/jupyter/server.py"],
        "env": {
            "PYTHONPATH": os.getenv("PYTHONPATH", "./"),
            "EXECUTION_MODE": "local",
            "KERNEL_NAME": "python3",
            "TIMEOUT": "300"
        }
    }
    
    async with StandaloneMCPClient("", "stdio", **config) as client:
        # 获取工具列表
        tools = await client.list_tools()
        print(f"本地可用工具: {tools}")
        
        # 执行代码
        result = await client.call_tool(
            "execute_code",
            {
                "code": """
print("=== 本地Stdio MCP客户端测试 ===")

# 测试本地计算能力
import math

def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 计算斐波那契数列
fib_numbers = [fibonacci(i) for i in range(10)]
print(f"斐波那契数列前10项: {fib_numbers}")

# 测试数学计算
result = math.sqrt(2) * math.pi
print(f"√2 × π = {result:.6f}")
                """
            }
        )
        print("本地执行结果:", result)


async def demo_error_handling():
    """演示错误处理"""
    print("\n🛡️ === 错误处理演示 ===")
    
    async def test_with_retry(url: str, max_retries: int = 3):
        """带重试的连接测试"""
        for attempt in range(max_retries):
            try:
                async with sse_client(url, timeout=5.0) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        
                        # 测试工具调用
                        result = await session.call_tool(
                            "execute_code",
                            {"code": "print('连接测试成功')"}
                        )
                        
                        print(f"✅ 连接成功 (尝试 {attempt + 1})")
                        return result
                        
            except Exception as e:
                print(f"❌ 连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    print("🚫 所有重试都失败了")
                    raise
                
                # 指数退避
                wait_time = 2 ** attempt
                print(f"⏳ 等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)
    
    # 测试正常连接
    try:
        await test_with_retry("http://localhost:8900/sse")
    except Exception as e:
        print(f"连接最终失败: {e}")
    
    # 测试错误连接
    try:
        await test_with_retry("http://localhost:9999/sse")  # 错误端口
    except Exception as e:
        print(f"预期的连接失败: {e}")


async def demo_parallel_calls():
    """演示并行工具调用"""
    print("\n⚡ === 并行调用演示 ===")
    
    # 定义多个调用任务
    async def call_task(name: str, url: str, tool_name: str, arguments: Dict[str, Any]):
        """单个调用任务"""
        try:
            async with StandaloneMCPClient(url, "sse") as client:
                result = await client.call_tool(tool_name, arguments)
                return {"name": name, "status": "success", "result": result}
        except Exception as e:
            return {"name": name, "status": "error", "error": str(e)}
    
    # 创建并行任务
    tasks = [
        call_task(
            "代码执行1",
            "http://localhost:8900/sse",
            "execute_code",
            {"code": "print('并行任务1: 计算1+1 =', 1+1)"}
        ),
        call_task(
            "代码执行2",
            "http://localhost:8900/sse",
            "execute_code",
            {"code": "print('并行任务2: 计算2*3 =', 2*3)"}
        ),
        call_task(
            "AI搜索",
            "http://localhost:8903/sse",
            "aisearch_retrieve",
            {"question": "Python基础语法", "recall_num": 2}
        )
    ]
    
    # 并行执行所有任务
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理结果
    for result in results:
        if isinstance(result, Exception):
            print(f"❌ 任务异常: {result}")
        else:
            print(f"✅ {result['name']}: {result['status']}")
            if result['status'] == 'error':
                print(f"   错误: {result['error']}")


async def main():
    """主演示函数"""
    print("🚀 MCP 独立客户端演示开始")
    print("=" * 60)
    
    try:
        # 1. Jupyter客户端演示
        await demo_jupyter_client()
        
        # 2. SQL生成演示
        await demo_sql_generation()
        
        # 3. 代码生成演示
        await demo_code_generation()
        
        # 4. AI搜索演示
        await demo_ai_search()
        
        # 5. 本地Stdio客户端演示
        await demo_stdio_local_client()
        
        # 6. 错误处理演示
        await demo_error_handling()
        
        # 7. 并行调用演示
        await demo_parallel_calls()
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断演示")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 MCP 独立客户端演示完成")


if __name__ == "__main__":
    # 设置日志级别
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # 运行演示
    asyncio.run(main())
