#!/usr/bin/env python3
"""
最简单的MCP工具调用示例
适合快速测试和学习MCP客户端调用
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcp import ClientSession
from mcp.client.sse import sse_client


async def simple_call_example():
    """最简单的MCP调用示例"""
    
    # MCP服务器地址 (确保服务器已启动)
    jupyter_url = "http://localhost:8900/sse"
    
    print("🔗 连接到Jupyter MCP服务器...")
    
    try:
        # 建立SSE连接
        async with sse_client(jupyter_url) as (read, write):
            print("✅ SSE连接建立成功")
            
            # 创建客户端会话
            async with ClientSession(read, write) as session:
                print("✅ 客户端会话创建成功")
                
                # 初始化会话
                await session.initialize()
                print("✅ 会话初始化完成")
                
                # 获取可用工具
                tools = await session.list_tools()
                tool_names = [tool.name for tool in tools.tools]
                print(f"📋 可用工具: {tool_names}")
                
                # 调用execute_code工具
                print("\n🐍 执行Python代码...")
                result = await session.call_tool(
                    "execute_code",
                    arguments={
                        "code": """
print("Hello from MCP!")
print("这是通过独立客户端调用的代码")

# 简单计算
a = 10
b = 20
result = a + b
print(f"{a} + {b} = {result}")

# 列表操作
numbers = [1, 2, 3, 4, 5]
squared = [x**2 for x in numbers]
print(f"原数字: {numbers}")
print(f"平方后: {squared}")
                        """
                    }
                )
                
                print("✅ 代码执行完成")
                print("📤 执行结果:")
                
                # 解析输出结果
                if result and hasattr(result, 'content'):
                    for output in result.content:
                        if hasattr(output, 'text'):
                            print(output.text)
                else:
                    print(result)
                
                return result
                
    except Exception as e:
        print(f"❌ 调用失败: {e}")
        raise


async def call_multiple_tools():
    """调用多个不同的工具"""
    
    print("\n🛠️ === 调用多个工具演示 ===")
    
    # 服务器配置
    servers = {
        "jupyter": "http://localhost:8900/sse",
        "generate_sql": "http://localhost:8901/sse",
        "nl2code": "http://localhost:8902/sse",
        "aisearch": "http://localhost:8903/sse"
    }
    
    # 1. 调用Jupyter工具
    print("\n1️⃣ 调用Jupyter代码执行工具")
    try:
        async with sse_client(servers["jupyter"]) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                result = await session.call_tool(
                    "execute_code",
                    {"code": "print('Jupyter工具调用成功')\nprint('当前时间:', __import__('datetime').datetime.now())"}
                )
                print("✅ Jupyter调用成功")
                
    except Exception as e:
        print(f"❌ Jupyter调用失败: {e}")
    
    # 2. 调用SQL生成工具
    print("\n2️⃣ 调用SQL生成工具")
    try:
        async with sse_client(servers["generate_sql"]) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                result = await session.call_tool(
                    "generate_sql",
                    {
                        "engine_type": "dlc",
                        "params": {
                            "question": "查询用户表中年龄大于25的用户信息",
                            "app_id": "test_app",
                            "db_info": '[{"DbName": "test_db", "TableList": ["users"]}]',
                            "data_engine_name": "test_engine",
                            "mcp_url": {"DLC": "http://localhost:8080/sse"}
                        }
                    }
                )
                print("✅ SQL生成成功")
                print(f"生成的SQL: {result.get('sql', '')}")
                
    except Exception as e:
        print(f"❌ SQL生成失败: {e}")
    
    # 3. 调用代码生成工具
    print("\n3️⃣ 调用代码生成工具")
    try:
        async with sse_client(servers["nl2code"]) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                result = await session.call_tool(
                    "nl2code",
                    {
                        "params": {
                            "user_instruction": "创建一个函数来计算列表中数字的统计信息",
                            "data_type": "List",
                            "model_name": "DeepSeek-V3-0324"
                        }
                    }
                )
                print("✅ 代码生成成功")
                print("生成的代码:")
                print(result.get('python_code', ''))
                
    except Exception as e:
        print(f"❌ 代码生成失败: {e}")
    
    # 4. 调用AI搜索工具
    print("\n4️⃣ 调用AI搜索工具")
    try:
        async with sse_client(servers["aisearch"]) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                result = await session.call_tool(
                    "aisearch_retrieve",
                    {
                        "question": "Python数据分析最佳实践",
                        "recall_num": 2
                    }
                )
                print("✅ AI搜索成功")
                print(f"找到 {len(result.get('aisearch', []))} 个相关文档")
                
    except Exception as e:
        print(f"❌ AI搜索失败: {e}")


def sync_call_example():
    """同步调用示例 (在非异步环境中使用)"""
    
    print("\n🔄 === 同步调用示例 ===")
    
    async def async_call():
        """内部异步调用"""
        url = "http://localhost:8900/sse"
        
        async with sse_client(url) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                return await session.call_tool(
                    "execute_code",
                    {"code": "print('同步调用示例')\nresult = 42\nprint(f'答案是: {result}')"}
                )
    
    # 在同步环境中运行异步代码
    result = asyncio.run(async_call())
    print("同步调用结果:", result)
    return result


async def interactive_demo():
    """交互式演示"""
    
    print("\n🎮 === 交互式MCP客户端 ===")
    print("输入Python代码，按回车执行 (输入'quit'退出)")
    
    url = "http://localhost:8900/sse"
    
    try:
        async with sse_client(url) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                print("✅ 连接建立，可以开始输入代码")
                
                while True:
                    try:
                        # 获取用户输入
                        code = input("\n>>> ")
                        
                        if code.lower() in ['quit', 'exit', 'q']:
                            print("👋 退出交互式模式")
                            break
                        
                        if not code.strip():
                            continue
                        
                        # 执行代码
                        result = await session.call_tool(
                            "execute_code",
                            {"code": code}
                        )
                        
                        # 显示结果
                        if result and hasattr(result, 'content'):
                            for output in result.content:
                                if hasattr(output, 'text'):
                                    print(output.text)
                        else:
                            print(result)
                            
                    except KeyboardInterrupt:
                        print("\n⏹️ 用户中断")
                        break
                    except Exception as e:
                        print(f"❌ 执行错误: {e}")
                        
    except Exception as e:
        print(f"❌ 连接失败: {e}")


def print_usage():
    """打印使用说明"""
    print("""
🔧 MCP独立客户端使用说明

使用方法:
    python examples/simple_mcp_call.py [选项]

选项:
    --simple        运行简单调用示例 (默认)
    --multiple      运行多工具调用示例
    --sync          运行同步调用示例
    --interactive   运行交互式模式
    --help          显示此帮助信息

前提条件:
    1. 确保MCP服务器已启动:
       python infra/mcp/jupyter/server.py &
       python infra/mcp/manager/server/generate_sql.py &
       python infra/mcp/manager/server/nl2code.py &
       python infra/mcp/manager/server/aisearch.py &
    
    2. 确保服务器监听在正确的端口:
       - Jupyter: http://localhost:8900/sse
       - SQL生成: http://localhost:8901/sse
       - 代码生成: http://localhost:8902/sse
       - AI搜索: http://localhost:8903/sse

示例:
    # 运行简单示例
    python examples/simple_mcp_call.py --simple
    
    # 运行交互式模式
    python examples/simple_mcp_call.py --interactive
    """)


async def main():
    """主函数"""
    
    # 解析命令行参数
    args = sys.argv[1:] if len(sys.argv) > 1 else ["--simple"]
    
    if "--help" in args or "-h" in args:
        print_usage()
        return
    
    print("🚀 MCP独立客户端演示")
    print("=" * 50)
    
    try:
        if "--simple" in args or len(args) == 0:
            await simple_call_example()
        
        if "--multiple" in args:
            await call_multiple_tools()
        
        if "--sync" in args:
            sync_call_example()
        
        if "--interactive" in args:
            await interactive_demo()
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
