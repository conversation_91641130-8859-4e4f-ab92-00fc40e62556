# 🚀 如何独立使用MCP客户端

## 📋 快速上手指南

### 1. 环境准备

```bash
# 确保在项目根目录
cd /Users/<USER>/codebase/intellix-ds-agent

# 激活虚拟环境 (如果使用)
source .venv/bin/activate

# 安装MCP依赖 (如果未安装)
pip install mcp
```

### 2. 启动MCP服务器

```bash
# 方式1: 一键启动所有服务器
python scripts/start_mcp_servers.py

# 方式2: 启动特定服务器
python scripts/start_mcp_servers.py --servers jupyter generate_sql

# 方式3: 启动并测试
python scripts/start_mcp_servers.py --test

# 方式4: 交互式启动
python scripts/start_mcp_servers.py --interactive
```

### 3. 验证服务器运行

```bash
# 检查端口监听
netstat -tuln | grep -E "(8900|8901|8902|8903|8904)"

# 应该看到类似输出:
# tcp4  0  0  127.0.0.1.8900  *.*  LISTEN
# tcp4  0  0  127.0.0.1.8901  *.*  LISTEN
# tcp4  0  0  127.0.0.1.8902  *.*  LISTEN
# tcp4  0  0  127.0.0.1.8903  *.*  LISTEN
```

### 4. 运行客户端示例

```bash
# 运行简单示例
python examples/simple_mcp_call.py --simple

# 运行完整示例
python examples/standalone_mcp_client.py

# 运行交互式示例
python examples/simple_mcp_call.py --interactive
```

## 💻 代码示例

### 最简单的调用

```python
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def simple_call():
    """最简单的MCP调用"""
    
    # 连接到Jupyter服务器
    async with sse_client("http://localhost:8900/sse") as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            # 执行Python代码
            result = await session.call_tool(
                "execute_code",
                arguments={"code": "print('Hello, MCP!')"}
            )
            
            print("结果:", result)

# 运行
asyncio.run(simple_call())
```

### 实用的工具调用

```python
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def practical_examples():
    """实用的MCP工具调用示例"""
    
    # 1. 数据分析示例
    print("=== 数据分析示例 ===")
    async with sse_client("http://localhost:8900/sse") as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            result = await session.call_tool(
                "execute_code",
                arguments={
                    "code": """
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 创建示例数据
np.random.seed(42)
data = {
    'date': pd.date_range('2024-01-01', periods=100),
    'sales': np.random.randint(100, 1000, 100),
    'profit': np.random.randint(10, 100, 100)
}
df = pd.DataFrame(data)

# 基本统计
print("数据概览:")
print(df.describe())

# 计算相关性
correlation = df[['sales', 'profit']].corr()
print("\\n销售额与利润相关性:")
print(correlation)

# 创建图表
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
plt.plot(df['date'], df['sales'])
plt.title('销售趋势')
plt.xticks(rotation=45)

plt.subplot(1, 2, 2)
plt.scatter(df['sales'], df['profit'])
plt.xlabel('销售额')
plt.ylabel('利润')
plt.title('销售额 vs 利润')

plt.tight_layout()
plt.show()

print("\\n分析完成!")
                    """,
                    "required_packages": ["pandas", "numpy", "matplotlib"]
                }
            )
            
            print("数据分析执行完成")
    
    # 2. SQL生成示例
    print("\n=== SQL生成示例 ===")
    async with sse_client("http://localhost:8901/sse") as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            result = await session.call_tool(
                "generate_sql",
                arguments={
                    "engine_type": "dlc",
                    "params": {
                        "question": "查询每个部门的员工数量和平均薪资",
                        "app_id": "hr_app",
                        "db_info": '[{"DbName": "hr_db", "TableList": ["employees", "departments"]}]',
                        "data_engine_name": "hr_engine",
                        "mcp_url": {"DLC": "http://localhost:8080/sse"}
                    }
                }
            )
            
            print("生成的SQL:")
            print(result.get("sql", ""))
    
    # 3. 代码生成示例
    print("\n=== 代码生成示例 ===")
    async with sse_client("http://localhost:8902/sse") as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            result = await session.call_tool(
                "nl2code",
                arguments={
                    "params": {
                        "user_instruction": "创建一个函数来计算投资回报率，包含本金、收益和时间参数",
                        "scenario": "general",
                        "model_name": "DeepSeek-V3-0324"
                    }
                }
            )
            
            print("生成的代码:")
            print(result.get("python_code", ""))

# 运行示例
asyncio.run(practical_examples())
```

## 🔧 自定义客户端类

```python
# my_mcp_client.py
import asyncio
from typing import Dict, Any, Optional
from mcp import ClientSession
from mcp.client.sse import sse_client

class MyMCPClient:
    """自定义MCP客户端"""
    
    def __init__(self):
        self.servers = {
            "jupyter": "http://localhost:8900/sse",
            "sql": "http://localhost:8901/sse",
            "code": "http://localhost:8902/sse",
            "search": "http://localhost:8903/sse"
        }
    
    async def execute_python(self, code: str, packages: list = None) -> Dict[str, Any]:
        """执行Python代码"""
        async with sse_client(self.servers["jupyter"]) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                args = {"code": code}
                if packages:
                    args["required_packages"] = packages
                
                return await session.call_tool("execute_code", arguments=args)
    
    async def generate_sql(self, question: str, tables: list) -> str:
        """生成SQL查询"""
        async with sse_client(self.servers["sql"]) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                result = await session.call_tool(
                    "generate_sql",
                    arguments={
                        "engine_type": "dlc",
                        "params": {
                            "question": question,
                            "db_info": json.dumps([{"DbName": "db", "TableList": tables}]),
                            "app_id": "my_app"
                        }
                    }
                )
                
                return result.get("sql", "")
    
    async def generate_code(self, instruction: str, context: Dict[str, Any] = None) -> str:
        """生成Python代码"""
        async with sse_client(self.servers["code"]) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                params = {
                    "user_instruction": instruction,
                    "model_name": "DeepSeek-V3-0324"
                }
                
                if context:
                    params.update(context)
                
                result = await session.call_tool(
                    "nl2code",
                    arguments={"params": params}
                )
                
                return result.get("python_code", "")
    
    async def search_knowledge(self, question: str, num_results: int = 5) -> list:
        """搜索知识库"""
        async with sse_client(self.servers["search"]) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                result = await session.call_tool(
                    "aisearch_retrieve",
                    arguments={
                        "question": question,
                        "recall_num": num_results
                    }
                )
                
                return result.get("aisearch", [])

# 使用示例
async def use_custom_client():
    """使用自定义客户端"""
    
    client = MyMCPClient()
    
    # 1. 执行数据分析
    print("执行数据分析...")
    await client.execute_python("""
import pandas as pd
data = {'A': [1, 2, 3], 'B': [4, 5, 6]}
df = pd.DataFrame(data)
print(df.sum())
    """, ["pandas"])
    
    # 2. 生成SQL
    print("生成SQL...")
    sql = await client.generate_sql("查询所有用户", ["users"])
    print(f"SQL: {sql}")
    
    # 3. 生成代码
    print("生成代码...")
    code = await client.generate_code("计算列表平均值的函数")
    print(f"代码: {code}")
    
    # 4. 搜索知识
    print("搜索知识...")
    docs = await client.search_knowledge("Python数据处理")
    print(f"找到 {len(docs)} 个文档")

# 运行
asyncio.run(use_custom_client())
```

## 📞 获取帮助

### 检查服务器状态

```python
# check_status.py
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def check_all_servers():
    """检查所有服务器状态"""
    
    servers = {
        "Jupyter": "http://localhost:8900/sse",
        "SQL生成": "http://localhost:8901/sse", 
        "代码生成": "http://localhost:8902/sse",
        "AI搜索": "http://localhost:8903/sse",
        "广告生成": "http://localhost:8904/sse"
    }
    
    print("🔍 检查MCP服务器状态...")
    print("=" * 60)
    
    for name, url in servers.items():
        try:
            async with sse_client(url, timeout=3.0) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    tools = await session.list_tools()
                    
                    print(f"✅ {name:<10} | 在线 | {len(tools.tools)} 个工具 | {url}")
                    
        except Exception as e:
            print(f"❌ {name:<10} | 离线 | 错误: {str(e)[:30]}... | {url}")

asyncio.run(check_all_servers())
```

### 常用命令

```bash
# 启动服务器
python scripts/start_mcp_servers.py

# 检查状态
python check_status.py

# 运行简单测试
python examples/simple_mcp_call.py

# 停止所有服务器 (Ctrl+C 或者)
pkill -f "mcp.*server"
```

## 🎯 总结

通过以上步骤，您可以：

1. **🚀 快速启动**: 使用脚本一键启动所有MCP服务器
2. **🔗 独立连接**: 直接使用MCP客户端连接服务器
3. **🛠️ 调用工具**: 调用各种专业化工具 (代码执行、SQL生成、AI搜索等)
4. **⚡ 并行处理**: 实现高性能的并行工具调用
5. **🛡️ 错误处理**: 完善的错误处理和重试机制
6. **📊 监控调试**: 实时监控和调试工具

### 关键文件

- **启动脚本**: `scripts/start_mcp_servers.py`
- **简单示例**: `examples/simple_mcp_call.py`
- **完整示例**: `examples/standalone_mcp_client.py`
- **工具函数**: `utils/mcp_utils.py` (需要创建)
- **状态检查**: `check_status.py` (需要创建)

### 服务器地址

- **Jupyter**: http://localhost:8900/sse
- **SQL生成**: http://localhost:8901/sse
- **代码生成**: http://localhost:8902/sse
- **AI搜索**: http://localhost:8903/sse
- **广告生成**: http://localhost:8904/sse

现在您可以完全独立地使用MCP客户端调用各种工具，无需依赖完整的MCPManager系统！

---

*使用指南版本: v1.0*  
*最后更新: 2025-08-14*
