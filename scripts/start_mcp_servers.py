#!/usr/bin/env python3
"""
MCP服务器启动脚本
用于快速启动所有MCP服务器，方便独立客户端调用
"""

import os
import sys
import time
import signal
import subprocess
import asyncio
import json
from typing import Dict, List, Optional
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from mcp import ClientSession
from mcp.client.sse import sse_client


class MCPServerManager:
    """MCP服务器管理器"""
    
    def __init__(self):
        self.servers = {}
        self.project_root = project_root
        
        # 服务器配置
        self.server_configs = {
            "jupyter": {
                "script": "infra/mcp/jupyter/server.py",
                "port": 8900,
                "env": {
                    "PYTHONPATH": str(self.project_root),
                    "EXECUTION_MODE": "local",
                    "KERNEL_NAME": "python3",
                    "TIMEOUT": "300"
                }
            },
            "generate_sql": {
                "script": "infra/mcp/manager/server/generate_sql.py",
                "port": 8901,
                "env": {
                    "PYTHONPATH": str(self.project_root),
                    "MODEL_NAME": "DeepSeek-V3-0324"
                }
            },
            "nl2code": {
                "script": "infra/mcp/manager/server/nl2code.py",
                "port": 8902,
                "env": {
                    "PYTHONPATH": str(self.project_root),
                    "MODEL_NAME": "DeepSeek-V3-0324"
                }
            },
            "aisearch": {
                "script": "infra/mcp/manager/server/aisearch.py",
                "port": 8903,
                "env": {
                    "PYTHONPATH": str(self.project_root),
                    "KNOWLEDGE_BASE_IDS": '["kb_001"]'
                }
            },
            "generate_ad": {
                "script": "infra/mcp/manager/server/generate_ad.py",
                "port": 8904,
                "env": {
                    "PYTHONPATH": str(self.project_root)
                }
            }
        }
    
    def start_server(self, server_name: str) -> bool:
        """启动单个服务器"""
        
        if server_name not in self.server_configs:
            print(f"❌ 未知的服务器: {server_name}")
            return False
        
        config = self.server_configs[server_name]
        script_path = self.project_root / config["script"]
        
        if not script_path.exists():
            print(f"❌ 服务器脚本不存在: {script_path}")
            return False
        
        print(f"🚀 启动服务器: {server_name}")
        print(f"   脚本: {script_path}")
        print(f"   端口: {config['port']}")
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env.update(config["env"])
            
            # 启动进程
            process = subprocess.Popen(
                [sys.executable, str(script_path)],
                env=env,
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.servers[server_name] = {
                "process": process,
                "config": config,
                "start_time": time.time()
            }
            
            print(f"✅ {server_name} 启动成功 (PID: {process.pid})")
            return True
            
        except Exception as e:
            print(f"❌ {server_name} 启动失败: {e}")
            return False
    
    def start_all_servers(self, servers: Optional[List[str]] = None) -> Dict[str, bool]:
        """启动所有或指定的服务器"""
        
        if servers is None:
            servers = list(self.server_configs.keys())
        
        results = {}
        
        print("🚀 启动MCP服务器集群...")
        print("=" * 50)
        
        for server_name in servers:
            results[server_name] = self.start_server(server_name)
            time.sleep(1)  # 避免端口冲突
        
        return results
    
    async def wait_for_servers(self, timeout: int = 30) -> Dict[str, bool]:
        """等待服务器启动完成"""
        
        print(f"\n⏳ 等待服务器启动完成 (超时: {timeout}秒)...")
        
        results = {}
        start_time = time.time()
        
        for server_name, server_info in self.servers.items():
            config = server_info["config"]
            url = f"http://localhost:{config['port']}/sse"
            
            print(f"🔍 检查 {server_name} 服务器状态...")
            
            # 等待服务器响应
            for attempt in range(timeout):
                try:
                    async with sse_client(url, timeout=2.0) as (read, write):
                        async with ClientSession(read, write) as session:
                            await session.initialize()
                            tools = await session.list_tools()
                            
                            print(f"✅ {server_name} 服务器就绪 (工具数: {len(tools.tools)})")
                            results[server_name] = True
                            break
                            
                except Exception:
                    if attempt == 0:
                        print(f"   等待 {server_name} 启动...", end="", flush=True)
                    else:
                        print(".", end="", flush=True)
                    
                    await asyncio.sleep(1)
                    
                    # 检查进程是否还在运行
                    process = server_info["process"]
                    if process.poll() is not None:
                        print(f"\n❌ {server_name} 进程已退出 (返回码: {process.returncode})")
                        results[server_name] = False
                        break
            else:
                print(f"\n⏰ {server_name} 启动超时")
                results[server_name] = False
        
        elapsed = time.time() - start_time
        print(f"\n⏱️ 服务器启动检查完成，耗时: {elapsed:.1f}秒")
        
        return results
    
    async def test_all_servers(self):
        """测试所有服务器"""
        
        print("\n🧪 测试所有服务器功能...")
        print("=" * 50)
        
        test_cases = {
            "jupyter": {
                "url": "http://localhost:8900/sse",
                "tool": "execute_code",
                "args": {"code": "print('Jupyter测试成功')"}
            },
            "generate_sql": {
                "url": "http://localhost:8901/sse",
                "tool": "generate_sql",
                "args": {
                    "engine_type": "dlc",
                    "params": {
                        "question": "测试查询",
                        "app_id": "test",
                        "db_info": '[{"DbName": "test", "TableList": ["test"]}]'
                    }
                }
            },
            "nl2code": {
                "url": "http://localhost:8902/sse",
                "tool": "nl2code",
                "args": {
                    "params": {
                        "user_instruction": "打印hello world",
                        "model_name": "DeepSeek-V3-0324"
                    }
                }
            },
            "aisearch": {
                "url": "http://localhost:8903/sse",
                "tool": "aisearch_retrieve",
                "args": {
                    "question": "测试搜索",
                    "recall_num": 1
                }
            }
        }
        
        results = {}
        
        for server_name, test_config in test_cases.items():
            if server_name not in self.servers:
                print(f"⏭️ 跳过未启动的服务器: {server_name}")
                continue
            
            print(f"🧪 测试 {server_name}...")
            
            try:
                async with sse_client(test_config["url"], timeout=5.0) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        
                        result = await session.call_tool(
                            test_config["tool"],
                            test_config["args"]
                        )
                        
                        print(f"✅ {server_name} 测试成功")
                        results[server_name] = True
                        
            except Exception as e:
                print(f"❌ {server_name} 测试失败: {e}")
                results[server_name] = False
        
        return results
    
    def stop_server(self, server_name: str):
        """停止单个服务器"""
        
        if server_name not in self.servers:
            print(f"⚠️ 服务器 {server_name} 未运行")
            return
        
        server_info = self.servers[server_name]
        process = server_info["process"]
        
        print(f"🛑 停止服务器: {server_name} (PID: {process.pid})")
        
        try:
            # 优雅停止
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=10)
                print(f"✅ {server_name} 已优雅停止")
            except subprocess.TimeoutExpired:
                # 强制停止
                process.kill()
                process.wait()
                print(f"⚡ {server_name} 已强制停止")
                
        except Exception as e:
            print(f"❌ 停止 {server_name} 失败: {e}")
        
        del self.servers[server_name]
    
    def stop_all_servers(self):
        """停止所有服务器"""
        
        print("\n🛑 停止所有MCP服务器...")
        
        server_names = list(self.servers.keys())
        for server_name in server_names:
            self.stop_server(server_name)
        
        print("✅ 所有服务器已停止")
    
    def get_server_status(self) -> Dict[str, Dict[str, Any]]:
        """获取服务器状态"""
        
        status = {}
        
        for server_name, server_info in self.servers.items():
            process = server_info["process"]
            config = server_info["config"]
            
            status[server_name] = {
                "pid": process.pid,
                "running": process.poll() is None,
                "port": config["port"],
                "uptime": time.time() - server_info["start_time"],
                "url": f"http://localhost:{config['port']}/sse"
            }
        
        return status
    
    def print_status(self):
        """打印服务器状态"""
        
        status = self.get_server_status()
        
        print("\n📊 MCP服务器状态:")
        print("=" * 70)
        print(f"{'服务器':<15} {'状态':<8} {'PID':<8} {'端口':<6} {'运行时间':<10} {'URL'}")
        print("-" * 70)
        
        for server_name, info in status.items():
            status_icon = "🟢" if info["running"] else "🔴"
            uptime = f"{info['uptime']:.0f}s"
            
            print(f"{server_name:<15} {status_icon:<8} {info['pid']:<8} {info['port']:<6} {uptime:<10} {info['url']}")


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n⚠️ 接收到信号 {signum}，正在停止服务器...")
    if hasattr(signal_handler, 'manager'):
        signal_handler.manager.stop_all_servers()
    sys.exit(0)


async def main():
    """主函数"""
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="MCP服务器管理器")
    parser.add_argument("--servers", nargs="+", 
                       choices=["jupyter", "generate_sql", "nl2code", "aisearch", "generate_ad"],
                       help="要启动的服务器列表")
    parser.add_argument("--test", action="store_true", help="启动后运行测试")
    parser.add_argument("--interactive", action="store_true", help="交互式模式")
    parser.add_argument("--stop", action="store_true", help="停止所有服务器")
    
    args = parser.parse_args()
    
    # 创建管理器
    manager = MCPServerManager()
    
    # 设置信号处理器
    signal_handler.manager = manager
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if args.stop:
            # 停止模式
            print("🛑 停止所有MCP服务器...")
            # 这里可以添加查找和停止现有进程的逻辑
            return
        
        # 启动服务器
        servers_to_start = args.servers or ["jupyter", "generate_sql", "nl2code", "aisearch"]
        
        print(f"🚀 启动MCP服务器: {', '.join(servers_to_start)}")
        start_results = manager.start_all_servers(servers_to_start)
        
        # 检查启动结果
        failed_servers = [name for name, success in start_results.items() if not success]
        if failed_servers:
            print(f"❌ 以下服务器启动失败: {failed_servers}")
            return
        
        # 等待服务器就绪
        ready_results = await manager.wait_for_servers()
        
        # 检查就绪状态
        not_ready = [name for name, ready in ready_results.items() if not ready]
        if not_ready:
            print(f"❌ 以下服务器未就绪: {not_ready}")
            manager.stop_all_servers()
            return
        
        # 打印状态
        manager.print_status()
        
        # 运行测试
        if args.test:
            print("\n🧪 运行功能测试...")
            test_results = await manager.test_all_servers()
            
            print("\n📋 测试结果:")
            for server_name, success in test_results.items():
                status = "✅ 通过" if success else "❌ 失败"
                print(f"  {server_name}: {status}")
        
        # 交互式模式
        if args.interactive:
            await interactive_mode(manager)
        else:
            # 非交互模式，等待用户中断
            print("\n✅ 所有服务器启动完成!")
            print("💡 服务器地址:")
            for server_name, info in manager.get_server_status().items():
                print(f"   {server_name}: {info['url']}")
            
            print("\n📖 使用示例:")
            print("   python examples/simple_mcp_call.py --simple")
            print("   python examples/standalone_mcp_client.py")
            
            print("\n⏹️ 按 Ctrl+C 停止所有服务器")
            
            # 等待中断信号
            try:
                while True:
                    await asyncio.sleep(1)
                    
                    # 检查进程状态
                    for server_name, server_info in list(manager.servers.items()):
                        process = server_info["process"]
                        if process.poll() is not None:
                            print(f"⚠️ 服务器 {server_name} 意外退出 (返回码: {process.returncode})")
                            del manager.servers[server_name]
                    
                    if not manager.servers:
                        print("❌ 所有服务器都已退出")
                        break
                        
            except KeyboardInterrupt:
                print("\n⏹️ 用户中断，正在停止服务器...")
    
    finally:
        manager.stop_all_servers()


async def interactive_mode(manager: MCPServerManager):
    """交互式模式"""
    
    print("\n🎮 进入交互式模式")
    print("可用命令:")
    print("  status  - 显示服务器状态")
    print("  test    - 运行功能测试")
    print("  call    - 调用工具")
    print("  quit    - 退出")
    
    while True:
        try:
            command = input("\n>>> ").strip().lower()
            
            if command == "quit" or command == "q":
                break
            elif command == "status":
                manager.print_status()
            elif command == "test":
                results = await manager.test_all_servers()
                print("测试结果:", results)
            elif command == "call":
                await interactive_tool_call(manager)
            elif command == "help":
                print("可用命令: status, test, call, quit")
            else:
                print(f"未知命令: {command}")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"命令执行错误: {e}")


async def interactive_tool_call(manager: MCPServerManager):
    """交互式工具调用"""
    
    # 显示可用服务器
    status = manager.get_server_status()
    running_servers = [name for name, info in status.items() if info["running"]]
    
    if not running_servers:
        print("❌ 没有运行中的服务器")
        return
    
    print(f"运行中的服务器: {', '.join(running_servers)}")
    
    # 选择服务器
    server_name = input("选择服务器: ").strip()
    if server_name not in running_servers:
        print(f"❌ 服务器 {server_name} 未运行")
        return
    
    # 获取工具列表
    server_info = status[server_name]
    url = server_info["url"]
    
    try:
        async with sse_client(url, timeout=5.0) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                tools = await session.list_tools()
                tool_names = [tool.name for tool in tools.tools]
                
                print(f"可用工具: {', '.join(tool_names)}")
                
                # 选择工具
                tool_name = input("选择工具: ").strip()
                if tool_name not in tool_names:
                    print(f"❌ 工具 {tool_name} 不存在")
                    return
                
                # 输入参数
                print("输入工具参数 (JSON格式):")
                args_input = input("参数: ").strip()
                
                try:
                    arguments = json.loads(args_input) if args_input else {}
                except json.JSONDecodeError:
                    print("❌ 参数格式错误，请使用JSON格式")
                    return
                
                # 调用工具
                print(f"🔧 调用工具: {tool_name}")
                result = await session.call_tool(tool_name, arguments)
                
                print("📤 调用结果:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
    except Exception as e:
        print(f"❌ 工具调用失败: {e}")


if __name__ == "__main__":
    print("🔧 MCP服务器管理器")
    print("=" * 50)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行错误: {e}")
        import traceback
        traceback.print_exc()
