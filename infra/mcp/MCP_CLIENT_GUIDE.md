# MCP 独立客户端调用指南

## 概述

本指南详细介绍如何独立创建MCP客户端来调用MCP系统中的各种工具，无需依赖完整的MCPManager。

## 🚀 快速开始

### 1. 基本依赖安装

```bash
# 安装MCP客户端依赖
pip install mcp

# 或者使用uv
uv add mcp
```

### 2. 最简单的客户端调用

```python
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def simple_mcp_call():
    """最简单的MCP工具调用示例"""
    
    # MCP服务器URL (SSE协议)
    url = "http://localhost:8900/sse"
    
    # 建立SSE连接
    async with sse_client(url) as (read, write):
        # 创建客户端会话
        async with ClientSession(read, write) as session:
            # 初始化会话
            await session.initialize()
            
            # 调用工具
            result = await session.call_tool(
                "execute_code",
                arguments={"code": "print('Hello from MCP!')"}
            )
            
            print("执行结果:", result)

# 运行示例
if __name__ == "__main__":
    asyncio.run(simple_mcp_call())
```

## 📡 支持的传输协议

### 1. SSE (Server-Sent Events) 协议

**适用场景**: Web应用、远程服务调用

```python
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client
from common.logger.logger import logger

async def sse_client_example():
    """SSE协议客户端示例"""
    
    # SSE服务器URL
    url = "http://localhost:8900/sse"
    
    # 可选参数
    timeout = 10.0  # HTTP连接超时
    sse_read_timeout = 300.0  # SSE读取超时
    
    try:
        async with sse_client(url, timeout=timeout, sse_read_timeout=sse_read_timeout) as (read, write):
            logger.info(f"SSE连接已建立: {url}")
            
            async with ClientSession(read, write) as session:
                # 初始化会话
                await session.initialize()
                logger.info("会话初始化完成")
                
                # 获取可用工具列表
                tools = await session.list_tools()
                logger.info(f"可用工具: {[tool.name for tool in tools.tools]}")
                
                # 调用具体工具
                result = await session.call_tool(
                    "execute_code",
                    arguments={
                        "code": """
import pandas as pd
import numpy as np

# 创建示例数据
data = {
    'name': ['Alice', 'Bob', 'Charlie'],
    'age': [25, 30, 35],
    'salary': [50000, 60000, 70000]
}
df = pd.DataFrame(data)

print("数据框信息:")
print(df.info())
print("\\n数据内容:")
print(df)
                        """,
                        "required_packages": ["pandas", "numpy"]
                    }
                )
                
                return result
                
    except Exception as e:
        logger.error(f"SSE调用失败: {e}")
        raise

# 运行示例
result = asyncio.run(sse_client_example())
print("执行结果:", result)
```

### 2. Stdio (标准输入输出) 协议

**适用场景**: 本地进程通信、开发调试

```python
import asyncio
import os
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from common.logger.logger import logger

async def stdio_client_example():
    """Stdio协议客户端示例"""
    
    # 配置服务器参数
    params = StdioServerParameters(
        command="python3",  # Python解释器路径
        args=["infra/mcp/jupyter/server.py"],  # 服务器脚本路径
        env={
            "PYTHONPATH": os.getenv("PYTHONPATH", "./"),
            "EXECUTION_MODE": "local",
            "IMAGE_TAG": "latest",
            "KERNEL_NAME": "python3",
            "TIMEOUT": "300"
        }
    )
    
    try:
        async with stdio_client(params) as (read, write):
            logger.info("Stdio连接已建立")
            
            async with ClientSession(read, write) as session:
                # 初始化会话
                await session.initialize()
                logger.info("会话初始化完成")
                
                # 获取工具列表
                tools = await session.list_tools()
                logger.info(f"可用工具: {[tool.name for tool in tools.tools]}")
                
                # 执行代码
                result = await session.call_tool(
                    "execute_code",
                    arguments={
                        "code": "print('Hello from Stdio MCP!')\nprint(2 + 3)"
                    }
                )
                
                return result
                
    except Exception as e:
        logger.error(f"Stdio调用失败: {e}")
        raise

# 运行示例
result = asyncio.run(stdio_client_example())
print("执行结果:", result)
```

## 🛠️ 工具调用示例

### 1. Jupyter 代码执行工具

```python
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def call_jupyter_tools():
    """调用Jupyter工具的完整示例"""
    
    url = "http://localhost:8900/sse"
    
    async with sse_client(url) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            # 1. 执行简单代码
            print("=== 执行简单代码 ===")
            result1 = await session.call_tool(
                "execute_code",
                arguments={"code": "print('Hello, MCP!')\nresult = 2 + 3\nprint(f'2 + 3 = {result}')"}
            )
            print("结果:", result1)
            
            # 2. 安装包并执行代码
            print("\n=== 安装包并执行代码 ===")
            result2 = await session.call_tool(
                "execute_code",
                arguments={
                    "code": """
import matplotlib.pyplot as plt
import numpy as np

# 生成数据
x = np.linspace(0, 10, 100)
y = np.sin(x)

# 创建图表
plt.figure(figsize=(10, 6))
plt.plot(x, y, 'b-', linewidth=2, label='sin(x)')
plt.title('正弦函数图像')
plt.xlabel('x')
plt.ylabel('sin(x)')
plt.legend()
plt.grid(True)
plt.show()

print('图表已生成')
                    """,
                    "required_packages": ["matplotlib", "numpy"]
                }
            )
            print("结果:", result2)
            
            # 3. 通过SQL加载数据
            print("\n=== 通过SQL加载数据 ===")
            result3 = await session.call_tool(
                "load_data_by_sql",
                arguments={
                    "sql": "SELECT * FROM users LIMIT 10",
                    "engine_type": "dlc",
                    "mcp_url": '{"DLC": "http://dlc-service:8080/sse"}',
                    "data_engine_name": "test-engine"
                }
            )
            print("结果:", result3)

# 运行示例
asyncio.run(call_jupyter_tools())
```

### 2. NL2SQL 工具调用

```python
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def call_nl2sql_tool():
    """调用NL2SQL工具生成SQL"""
    
    url = "http://localhost:8901/sse"  # NL2SQL服务器地址
    
    async with sse_client(url) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            # 调用SQL生成工具
            result = await session.call_tool(
                "generate_sql",
                arguments={
                    "engine_type": "dlc",
                    "params": {
                        "question": "查询销售额最高的前10个产品及其销售数据",
                        "app_id": "sales_app",
                        "sub_account_uin": "user123",
                        "trace_id": "trace_001",
                        "data_engine_name": "sales_engine",
                        "db_info": '[{"CatalogName": "SalesCatalog", "DbName": "sales_db", "TableList": ["products", "sales", "customers"]}]',
                        "is_sampling": False,
                        "mcp_url": {"DLC": "http://dlc-service:8080/sse"},
                        "type": "DLC",
                        "record_id": "sql_gen_001"
                    }
                }
            )
            
            print("生成的SQL:", result.get("sql"))
            print("推理过程:", result.get("reasoning"))
            print("涉及的表:", result.get("tables"))
            
            return result

# 运行示例
result = asyncio.run(call_nl2sql_tool())
```

### 3. NL2Code 工具调用

```python
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def call_nl2code_tool():
    """调用NL2Code工具生成Python代码"""
    
    url = "http://localhost:8902/sse"  # NL2Code服务器地址
    
    async with sse_client(url) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            # 调用代码生成工具
            result = await session.call_tool(
                "nl2code",
                arguments={
                    "params": {
                        "scenario": "correlation_analysis",
                        "user_instruction": "分析数据中各个数值列之间的相关性，生成相关性矩阵和热力图",
                        "env_dependencies": ["pandas", "numpy", "matplotlib", "seaborn"],
                        "global_vars": {"df": "sales_dataframe"},
                        "function_headers": [],
                        "previous_actions": [],
                        "data_type": "DataFrame",
                        "data_schema": "columns: [product_id, sales_amount, price, quantity, region, date]",
                        "model_name": "DeepSeek-V3-0324"
                    }
                }
            )
            
            print("生成的代码:")
            print(result.get("python_code"))
            print("\n所需包:", result.get("required_packages"))
            print("检测的场景:", result.get("detected_scenario"))
            
            return result

# 运行示例
result = asyncio.run(call_nl2code_tool())
```

### 4. AI搜索工具调用

```python
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def call_aisearch_tool():
    """调用AI搜索工具"""
    
    url = "http://localhost:8903/sse"  # AI搜索服务器地址
    
    async with sse_client(url) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            # 调用AI搜索工具
            result = await session.call_tool(
                "aisearch_retrieve",
                arguments={
                    "question": "如何使用pandas进行数据清洗和缺失值处理？",
                    "recall_num": 5
                }
            )
            
            print("搜索结果:")
            for i, doc in enumerate(result.get("aisearch", []), 1):
                print(f"\n文档 {i}:")
                print(f"内容: {doc['content'][:200]}...")
                print(f"相关性评分: {doc['score']}")
                print(f"重排序评分: {doc['rerank_score']}")
            
            return result

# 运行示例
result = asyncio.run(call_aisearch_tool())
```

## 🔧 高级客户端实现

### 1. 通用MCP客户端类

```python
import asyncio
import json
from typing import Dict, Any, Optional, List
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.client.stdio import stdio_client
from mcp import StdioServerParameters
from common.logger.logger import logger

class MCPClient:
    """
    通用MCP客户端，支持多种传输协议
    
    Features:
        - 支持SSE和Stdio协议
        - 自动连接管理
        - 错误处理和重试
        - 工具列表缓存
        - 性能监控
    """
    
    def __init__(self, server_config: Dict[str, Any]):
        """
        初始化MCP客户端
        
        Args:
            server_config: 服务器配置字典
            {
                "protocol": "sse" | "stdio",
                "url": "http://localhost:8900/sse",  # SSE协议使用
                "command": "python3",                # Stdio协议使用
                "args": ["server.py"],               # Stdio协议使用
                "env": {"KEY": "value"},             # 环境变量
                "timeout": 10.0,                     # 连接超时
                "sse_read_timeout": 300.0            # SSE读取超时
            }
        """
        self.config = server_config
        self.protocol = server_config["protocol"]
        self.session = None
        self.tools_cache = None
        
    async def connect(self):
        """建立连接"""
        if self.protocol == "sse":
            await self._connect_sse()
        elif self.protocol == "stdio":
            await self._connect_stdio()
        else:
            raise ValueError(f"不支持的协议类型: {self.protocol}")
    
    async def _connect_sse(self):
        """建立SSE连接"""
        url = self.config["url"]
        timeout = self.config.get("timeout", 10.0)
        sse_read_timeout = self.config.get("sse_read_timeout", 300.0)
        
        self.sse_context = sse_client(url, timeout=timeout, sse_read_timeout=sse_read_timeout)
        self.read, self.write = await self.sse_context.__aenter__()
        
        self.session = ClientSession(self.read, self.write)
        await self.session.__aenter__()
        await self.session.initialize()
        
        logger.info(f"SSE连接建立成功: {url}")
    
    async def _connect_stdio(self):
        """建立Stdio连接"""
        params = StdioServerParameters(
            command=self.config["command"],
            args=self.config["args"],
            env=self.config.get("env", {})
        )
        
        self.stdio_context = stdio_client(params)
        self.read, self.write = await self.stdio_context.__aenter__()
        
        self.session = ClientSession(self.read, self.write)
        await self.session.__aenter__()
        await self.session.initialize()
        
        logger.info("Stdio连接建立成功")
    
    async def list_tools(self) -> List[str]:
        """获取可用工具列表"""
        if not self.session:
            await self.connect()
        
        if self.tools_cache is None:
            tools_result = await self.session.list_tools()
            self.tools_cache = [tool.name for tool in tools_result.tools]
        
        return self.tools_cache
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """
        调用工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        if not self.session:
            await self.connect()
        
        try:
            result = await self.session.call_tool(tool_name, arguments=arguments)
            logger.info(f"工具调用成功: {tool_name}")
            return result
            
        except Exception as e:
            logger.error(f"工具调用失败: {tool_name}, 错误: {e}")
            raise
    
    async def close(self):
        """关闭连接"""
        if self.session:
            await self.session.__aexit__(None, None, None)
        
        if hasattr(self, 'sse_context'):
            await self.sse_context.__aexit__(None, None, None)
        elif hasattr(self, 'stdio_context'):
            await self.stdio_context.__aexit__(None, None, None)
        
        logger.info("连接已关闭")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

# 使用示例
async def use_mcp_client():
    """使用通用MCP客户端"""
    
    # SSE协议配置
    sse_config = {
        "protocol": "sse",
        "url": "http://localhost:8900/sse",
        "timeout": 10.0,
        "sse_read_timeout": 300.0
    }
    
    # 使用异步上下文管理器
    async with MCPClient(sse_config) as client:
        # 获取工具列表
        tools = await client.list_tools()
        print("可用工具:", tools)
        
        # 调用工具
        result = await client.call_tool(
            "execute_code",
            arguments={"code": "print('使用通用客户端调用成功!')"}
        )
        print("执行结果:", result)

# 运行示例
asyncio.run(use_mcp_client())
```

### 2. 批量工具调用客户端

```python
import asyncio
from typing import List, Dict, Any
from mcp import ClientSession
from mcp.client.sse import sse_client

class BatchMCPClient:
    """批量MCP工具调用客户端"""
    
    def __init__(self, server_urls: Dict[str, str]):
        """
        初始化批量客户端
        
        Args:
            server_urls: 服务器URL映射
            {
                "jupyter": "http://localhost:8900/sse",
                "generate_sql": "http://localhost:8901/sse",
                "nl2code": "http://localhost:8902/sse",
                "aisearch": "http://localhost:8903/sse"
            }
        """
        self.server_urls = server_urls
        self.sessions = {}
    
    async def connect_all(self):
        """连接所有服务器"""
        connection_tasks = []
        
        for server_name, url in self.server_urls.items():
            task = self._connect_server(server_name, url)
            connection_tasks.append(task)
        
        # 并行建立所有连接
        await asyncio.gather(*connection_tasks, return_exceptions=True)
    
    async def _connect_server(self, server_name: str, url: str):
        """连接单个服务器"""
        try:
            sse_context = sse_client(url)
            read, write = await sse_context.__aenter__()
            
            session = ClientSession(read, write)
            await session.__aenter__()
            await session.initialize()
            
            self.sessions[server_name] = {
                "session": session,
                "context": sse_context,
                "url": url
            }
            
            logger.info(f"服务器连接成功: {server_name} -> {url}")
            
        except Exception as e:
            logger.error(f"服务器连接失败: {server_name} -> {url}, 错误: {e}")
    
    async def call_tool(self, server_name: str, tool_name: str, 
                       arguments: Dict[str, Any]) -> Any:
        """调用指定服务器的工具"""
        if server_name not in self.sessions:
            raise ValueError(f"服务器 {server_name} 未连接")
        
        session_info = self.sessions[server_name]
        session = session_info["session"]
        
        try:
            result = await session.call_tool(tool_name, arguments=arguments)
            return result
        except Exception as e:
            logger.error(f"工具调用失败: {server_name}.{tool_name}, 错误: {e}")
            raise
    
    async def batch_call(self, calls: List[Dict[str, Any]]) -> List[Any]:
        """
        批量调用工具
        
        Args:
            calls: 调用列表
            [
                {
                    "server": "jupyter",
                    "tool": "execute_code",
                    "arguments": {"code": "print('test1')"}
                },
                {
                    "server": "generate_sql",
                    "tool": "generate_sql",
                    "arguments": {"params": {...}}
                }
            ]
            
        Returns:
            List[Any]: 调用结果列表
        """
        tasks = []
        
        for call in calls:
            task = self.call_tool(
                call["server"],
                call["tool"],
                call["arguments"]
            )
            tasks.append(task)
        
        # 并行执行所有调用
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    async def close_all(self):
        """关闭所有连接"""
        for server_name, session_info in self.sessions.items():
            try:
                session = session_info["session"]
                context = session_info["context"]
                
                await session.__aexit__(None, None, None)
                await context.__aexit__(None, None, None)
                
                logger.info(f"服务器连接已关闭: {server_name}")
                
            except Exception as e:
                logger.error(f"关闭连接失败: {server_name}, 错误: {e}")
        
        self.sessions.clear()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect_all()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_all()

# 使用示例
async def use_batch_client():
    """使用批量客户端"""
    
    server_urls = {
        "jupyter": "http://localhost:8900/sse",
        "generate_sql": "http://localhost:8901/sse",
        "nl2code": "http://localhost:8902/sse"
    }
    
    async with BatchMCPClient(server_urls) as client:
        # 批量调用多个工具
        calls = [
            {
                "server": "jupyter",
                "tool": "execute_code",
                "arguments": {"code": "print('批量调用测试1')"}
            },
            {
                "server": "nl2code",
                "tool": "nl2code",
                "arguments": {
                    "params": {
                        "user_instruction": "计算列表的平均值",
                        "data_type": "List",
                        "model_name": "DeepSeek-V3-0324"
                    }
                }
            }
        ]
        
        results = await client.batch_call(calls)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"调用 {i+1} 失败: {result}")
            else:
                print(f"调用 {i+1} 成功: {result}")

# 运行示例
asyncio.run(use_batch_client())
```

## 🔗 便捷调用函数

### 1. 简化的调用函数

```python
# infra/mcp/client_utils.py
import asyncio
from typing import Dict, Any, Optional
from mcp import ClientSession
from mcp.client.sse import sse_client
from common.logger.logger import logger

async def mcp_call_tool(url: str, tool_name: str, arguments: Dict[str, Any],
                       timeout: float = 10.0, sse_read_timeout: float = 300.0) -> Any:
    """
    便捷的MCP工具调用函数
    
    Args:
        url: MCP服务器SSE地址
        tool_name: 工具名称
        arguments: 工具参数
        timeout: HTTP连接超时
        sse_read_timeout: SSE读取超时
        
    Returns:
        工具执行结果
        
    Example:
        result = await mcp_call_tool(
            "http://localhost:8900/sse",
            "execute_code",
            {"code": "print('Hello!')"}
        )
    """
    try:
        async with sse_client(url, timeout=timeout, sse_read_timeout=sse_read_timeout) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                return await session.call_tool(tool_name, arguments=arguments)
                
    except Exception as e:
        logger.error(f"MCP工具调用失败: {tool_name} @ {url}, 错误: {e}")
        raise

def mcp_call_tool_sync(url: str, tool_name: str, arguments: Dict[str, Any],
                      timeout: float = 10.0, sse_read_timeout: float = 300.0) -> Any:
    """
    同步版本的MCP工具调用函数
    
    Args:
        url: MCP服务器SSE地址
        tool_name: 工具名称
        arguments: 工具参数
        timeout: HTTP连接超时
        sse_read_timeout: SSE读取超时
        
    Returns:
        工具执行结果
        
    Example:
        result = mcp_call_tool_sync(
            "http://localhost:8900/sse",
            "execute_code",
            {"code": "print('Hello!')"}
        )
    """
    return asyncio.run(mcp_call_tool(url, tool_name, arguments, timeout, sse_read_timeout))

# 预定义的服务器地址
DEFAULT_SERVERS = {
    "jupyter": "http://localhost:8900/sse",
    "generate_sql": "http://localhost:8901/sse",
    "nl2code": "http://localhost:8902/sse",
    "aisearch": "http://localhost:8903/sse",
    "generate_ad": "http://localhost:8904/sse"
}

async def call_jupyter_tool(tool_name: str, arguments: Dict[str, Any]) -> Any:
    """调用Jupyter工具的便捷函数"""
    return await mcp_call_tool(DEFAULT_SERVERS["jupyter"], tool_name, arguments)

async def call_sql_tool(arguments: Dict[str, Any]) -> Any:
    """调用SQL生成工具的便捷函数"""
    return await mcp_call_tool(DEFAULT_SERVERS["generate_sql"], "generate_sql", arguments)

async def call_code_tool(arguments: Dict[str, Any]) -> Any:
    """调用代码生成工具的便捷函数"""
    return await mcp_call_tool(DEFAULT_SERVERS["nl2code"], "nl2code", arguments)

async def call_search_tool(question: str, recall_num: Optional[int] = None) -> Any:
    """调用AI搜索工具的便捷函数"""
    arguments = {"question": question}
    if recall_num is not None:
        arguments["recall_num"] = recall_num
    return await mcp_call_tool(DEFAULT_SERVERS["aisearch"], "aisearch_retrieve", arguments)
```

### 2. 使用便捷函数的示例

```python
# examples/mcp_client_examples.py
import asyncio
from infra.mcp.client_utils import (
    call_jupyter_tool, call_sql_tool, call_code_tool, call_search_tool
)

async def main():
    """主要示例函数"""
    
    # 1. 执行Python代码
    print("=== 执行Python代码 ===")
    code_result = await call_jupyter_tool(
        "execute_code",
        {
            "code": """
import pandas as pd
import numpy as np

# 创建示例数据
np.random.seed(42)
data = {
    'product': ['A', 'B', 'C', 'D', 'E'],
    'sales': np.random.randint(100, 1000, 5),
    'price': np.random.uniform(10, 100, 5)
}
df = pd.DataFrame(data)

print("产品销售数据:")
print(df)

# 计算总销售额
total_sales = df['sales'].sum()
print(f"\\n总销售量: {total_sales}")

# 计算平均价格
avg_price = df['price'].mean()
print(f"平均价格: {avg_price:.2f}")
            """,
            "required_packages": ["pandas", "numpy"]
        }
    )
    print("代码执行结果:", code_result)
    
    # 2. 生成SQL查询
    print("\n=== 生成SQL查询 ===")
    sql_result = await call_sql_tool({
        "engine_type": "dlc",
        "params": {
            "question": "查询每个产品类别的总销售额，按销售额降序排列",
            "app_id": "demo_app",
            "sub_account_uin": "demo_user",
            "trace_id": "demo_trace",
            "data_engine_name": "demo_engine",
            "db_info": '[{"CatalogName": "DemoCatalog", "DbName": "demo_db", "TableList": ["products", "sales"]}]',
            "is_sampling": False,
            "mcp_url": {"DLC": "http://localhost:8080/sse"},
            "type": "DLC",
            "record_id": "demo_record"
        }
    })
    print("生成的SQL:", sql_result.get("sql"))
    print("推理过程:", sql_result.get("reasoning"))
    
    # 3. 生成Python代码
    print("\n=== 生成Python代码 ===")
    code_gen_result = await call_code_tool({
        "params": {
            "scenario": "summary_stats",
            "user_instruction": "对销售数据进行描述性统计分析，包括均值、中位数、标准差等",
            "env_dependencies": ["pandas", "numpy"],
            "global_vars": {"df": "sales_dataframe"},
            "data_type": "DataFrame",
            "data_schema": "columns: [product_id, sales_amount, price, category]",
            "model_name": "DeepSeek-V3-0324"
        }
    })
    print("生成的代码:")
    print(code_gen_result.get("python_code"))
    
    # 4. AI智能搜索
    print("\n=== AI智能搜索 ===")
    search_result = await call_search_tool(
        "如何使用pandas进行数据分组和聚合操作？",
        recall_num=3
    )
    print("搜索结果:")
    for i, doc in enumerate(search_result.get("aisearch", []), 1):
        print(f"文档{i}: {doc['content'][:100]}...")

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔧 实际部署和使用

### 1. 启动MCP服务器

```bash
#!/bin/bash
# start_mcp_servers.sh

# 启动Jupyter服务器
python3 infra/mcp/jupyter/server.py &
JUPYTER_PID=$!

# 启动SQL生成服务器
python3 infra/mcp/manager/server/generate_sql.py &
SQL_PID=$!

# 启动代码生成服务器
python3 infra/mcp/manager/server/nl2code.py &
CODE_PID=$!

# 启动AI搜索服务器
python3 infra/mcp/manager/server/aisearch.py &
SEARCH_PID=$!

echo "MCP服务器已启动:"
echo "Jupyter PID: $JUPYTER_PID"
echo "SQL生成 PID: $SQL_PID"
echo "代码生成 PID: $CODE_PID"
echo "AI搜索 PID: $SEARCH_PID"

# 等待服务器启动
sleep 5

# 测试连接
python3 -c "
import asyncio
from infra.mcp.client_utils import call_jupyter_tool

async def test():
    try:
        result = await call_jupyter_tool('execute_code', {'code': 'print(\"MCP服务器运行正常\")'})
        print('✅ MCP服务器测试成功')
    except Exception as e:
        print(f'❌ MCP服务器测试失败: {e}')

asyncio.run(test())
"
```

### 2. 客户端配置文件

```python
# config/mcp_client_config.py
from typing import Dict, Any

class MCPClientConfig:
    """MCP客户端配置"""
    
    # 默认服务器地址
    DEFAULT_SERVERS = {
        "jupyter": {
            "protocol": "sse",
            "url": "http://localhost:8900/sse",
            "timeout": 10.0,
            "sse_read_timeout": 300.0
        },
        "generate_sql": {
            "protocol": "sse",
            "url": "http://localhost:8901/sse",
            "timeout": 10.0,
            "sse_read_timeout": 300.0
        },
        "nl2code": {
            "protocol": "sse",
            "url": "http://localhost:8902/sse",
            "timeout": 10.0,
            "sse_read_timeout": 300.0
        },
        "aisearch": {
            "protocol": "sse",
            "url": "http://localhost:8903/sse",
            "timeout": 10.0,
            "sse_read_timeout": 300.0
        }
    }
    
    # 生产环境配置
    PRODUCTION_SERVERS = {
        "jupyter": {
            "protocol": "sse",
            "url": "https://mcp-jupyter.company.com/sse",
            "timeout": 15.0,
            "sse_read_timeout": 600.0
        },
        # ... 其他服务器配置
    }
    
    @classmethod
    def get_server_config(cls, server_name: str, environment: str = "development") -> Dict[str, Any]:
        """获取服务器配置"""
        if environment == "production":
            return cls.PRODUCTION_SERVERS.get(server_name)
        else:
            return cls.DEFAULT_SERVERS.get(server_name)
```

### 3. 完整的客户端应用示例

```python
# examples/complete_mcp_client.py
import asyncio
import json
from typing import Dict, Any
from infra.mcp.client_utils import MCPClient
from config.mcp_client_config import MCPClientConfig

class DataScienceWorkflow:
    """数据科学工作流客户端"""
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        self.clients = {}
    
    async def setup_clients(self):
        """设置客户端连接"""
        server_names = ["jupyter", "generate_sql", "nl2code", "aisearch"]
        
        for server_name in server_names:
            config = MCPClientConfig.get_server_config(server_name, self.environment)
            if config:
                client = MCPClient(config)
                await client.connect()
                self.clients[server_name] = client
                print(f"✅ {server_name} 客户端连接成功")
    
    async def run_data_analysis_workflow(self, question: str):
        """运行完整的数据分析工作流"""
        
        print(f"🚀 开始数据分析工作流: {question}")
        
        # 1. 生成SQL查询
        print("\n📊 步骤1: 生成SQL查询")
        sql_result = await self.clients["generate_sql"].call_tool(
            "generate_sql",
            {
                "engine_type": "dlc",
                "params": {
                    "question": question,
                    "app_id": "workflow_app",
                    "db_info": '[{"DbName": "analytics_db", "TableList": ["sales", "products", "customers"]}]',
                    "data_engine_name": "analytics_engine",
                    "mcp_url": {"DLC": "http://dlc-service:8080/sse"},
                    "type": "DLC"
                }
            }
        )
        
        generated_sql = sql_result.get("sql")
        print(f"生成的SQL: {generated_sql}")
        
        # 2. 加载数据
        print("\n📥 步骤2: 加载数据")
        data_result = await self.clients["jupyter"].call_tool(
            "load_data_by_sql",
            {
                "sql": generated_sql,
                "engine_type": "dlc",
                "mcp_url": '{"DLC": "http://dlc-service:8080/sse"}',
                "data_engine_name": "analytics_engine"
            }
        )
        
        print(f"数据加载完成，行数: {data_result.get('length', 0)}")
        
        # 3. 生成分析代码
        print("\n💻 步骤3: 生成分析代码")
        code_result = await self.clients["nl2code"].call_tool(
            "nl2code",
            {
                "params": {
                    "scenario": "summary_stats",
                    "user_instruction": f"对加载的数据进行详细的统计分析，回答问题: {question}",
                    "env_dependencies": ["pandas", "numpy", "matplotlib", "seaborn"],
                    "global_vars": {"df": "loaded_dataframe"},
                    "data_type": "DataFrame",
                    "data_schema": f"columns: {data_result.get('column_names', [])}",
                    "model_name": "DeepSeek-V3-0324"
                }
            }
        )
        
        analysis_code = code_result.get("python_code")
        print(f"生成的分析代码:\n{analysis_code}")
        
        # 4. 执行分析代码
        print("\n⚡ 步骤4: 执行分析代码")
        execution_result = await self.clients["jupyter"].call_tool(
            "execute_code",
            {
                "code": analysis_code,
                "required_packages": code_result.get("required_packages", [])
            }
        )
        
        print("分析执行完成")
        
        # 5. 搜索相关文档
        print("\n🔍 步骤5: 搜索相关文档")
        search_result = await self.clients["aisearch"].call_tool(
            "aisearch_retrieve",
            {
                "question": f"关于 {question} 的更多信息和最佳实践",
                "recall_num": 3
            }
        )
        
        print("相关文档:")
        for doc in search_result.get("aisearch", []):
            print(f"- {doc['content'][:100]}...")
        
        return {
            "sql": generated_sql,
            "data_info": data_result,
            "analysis_code": analysis_code,
            "execution_result": execution_result,
            "related_docs": search_result
        }
    
    async def cleanup(self):
        """清理客户端连接"""
        for name, client in self.clients.items():
            try:
                await client.close()
                print(f"✅ {name} 客户端连接已关闭")
            except Exception as e:
                print(f"❌ {name} 客户端关闭失败: {e}")

# 使用示例
async def main():
    """主函数"""
    workflow = DataScienceWorkflow()
    
    try:
        # 设置客户端
        await workflow.setup_clients()
        
        # 运行工作流
        result = await workflow.run_data_analysis_workflow(
            "分析每个产品类别的销售趋势和盈利能力"
        )
        
        print("\n🎉 工作流执行完成!")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    finally:
        # 清理资源
        await workflow.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔍 调试和故障排除

### 1. 连接测试工具

```python
# tools/test_mcp_connection.py
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def test_mcp_connection(url: str, expected_tools: list = None):
    """测试MCP服务器连接"""
    
    print(f"🔍 测试MCP连接: {url}")
    
    try:
        async with sse_client(url, timeout=5.0) as (read, write):
            print("✅ SSE连接建立成功")
            
            async with ClientSession(read, write) as session:
                await session.initialize()
                print("✅ 会话初始化成功")
                
                # 获取工具列表
                tools = await session.list_tools()
                tool_names = [tool.name for tool in tools.tools]
                print(f"✅ 可用工具: {tool_names}")
                
                # 检查期望的工具
                if expected_tools:
                    missing_tools = set(expected_tools) - set(tool_names)
                    if missing_tools:
                        print(f"⚠️ 缺少期望的工具: {missing_tools}")
                    else:
                        print("✅ 所有期望的工具都可用")
                
                # 测试简单工具调用
                if "execute_code" in tool_names:
                    test_result = await session.call_tool(
                        "execute_code",
                        arguments={"code": "print('连接测试成功')"}
                    )
                    print("✅ 工具调用测试成功")
                
                return True
                
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

# 测试所有服务器
async def test_all_servers():
    """测试所有MCP服务器"""
    
    servers = {
        "jupyter": {
            "url": "http://localhost:8900/sse",
            "expected_tools": ["execute_code", "load_data_by_sql"]
        },
        "generate_sql": {
            "url": "http://localhost:8901/sse",
            "expected_tools": ["generate_sql"]
        },
        "nl2code": {
            "url": "http://localhost:8902/sse",
            "expected_tools": ["nl2code"]
        },
        "aisearch": {
            "url": "http://localhost:8903/sse",
            "expected_tools": ["aisearch_retrieve"]
        }
    }
    
    results = {}
    
    for server_name, config in servers.items():
        print(f"\n{'='*50}")
        print(f"测试服务器: {server_name}")
        print(f"{'='*50}")
        
        success = await test_mcp_connection(
            config["url"],
            config["expected_tools"]
        )
        
        results[server_name] = success
    
    print(f"\n{'='*50}")
    print("测试结果汇总:")
    print(f"{'='*50}")
    
    for server_name, success in results.items():
        status = "✅ 正常" if success else "❌ 异常"
        print(f"{server_name}: {status}")

if __name__ == "__main__":
    asyncio.run(test_all_servers())
```

### 2. 错误处理示例

```python
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.shared.exceptions import McpError

async def robust_mcp_call(url: str, tool_name: str, arguments: Dict[str, Any],
                         max_retries: int = 3):
    """带重试机制的MCP调用"""
    
    for attempt in range(max_retries):
        try:
            async with sse_client(url, timeout=10.0) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    result = await session.call_tool(tool_name, arguments=arguments)
                    return result
                    
        except McpError as e:
            print(f"MCP协议错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(2 ** attempt)  # 指数退避
            
        except ConnectionError as e:
            print(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(2 ** attempt)
            
        except Exception as e:
            print(f"未知错误: {e}")
            raise

# 使用示例
async def example_with_retry():
    """使用重试机制的示例"""
    
    try:
        result = await robust_mcp_call(
            "http://localhost:8900/sse",
            "execute_code",
            {"code": "print('重试机制测试')"},
            max_retries=3
        )
        print("调用成功:", result)
        
    except Exception as e:
        print(f"最终调用失败: {e}")

asyncio.run(example_with_retry())
```

## 📋 总结

通过以上示例，您可以：

1. **🔌 直接连接**: 使用 `sse_client` 或 `stdio_client` 直接连接MCP服务器
2. **🛠️ 调用工具**: 通过 `ClientSession.call_tool()` 调用具体工具
3. **⚡ 异步处理**: 使用异步编程提高性能
4. **🔄 批量操作**: 实现批量工具调用和并行处理
5. **🛡️ 错误处理**: 实现完善的错误处理和重试机制
6. **📊 监控调试**: 添加日志记录和性能监控

### 关键要点

- **协议选择**: SSE适合远程调用，Stdio适合本地调用
- **会话管理**: 使用异步上下文管理器自动管理连接
- **错误处理**: 实现重试机制和异常捕获
- **性能优化**: 使用连接复用和并行调用

这样您就可以独立创建MCP客户端，无需依赖完整的MCPManager系统！

## 🎯 实际使用步骤

### 步骤1: 启动MCP服务器

```bash
# 方式1: 使用管理脚本启动所有服务器
python scripts/start_mcp_servers.py

# 方式2: 手动启动特定服务器
python scripts/start_mcp_servers.py --servers jupyter generate_sql

# 方式3: 启动并运行测试
python scripts/start_mcp_servers.py --test

# 方式4: 交互式模式
python scripts/start_mcp_servers.py --interactive
```

### 步骤2: 验证服务器状态

```python
# test_connection.py
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

async def test_connection():
    """测试MCP服务器连接"""

    servers = {
        "jupyter": "http://localhost:8900/sse",
        "generate_sql": "http://localhost:8901/sse",
        "nl2code": "http://localhost:8902/sse",
        "aisearch": "http://localhost:8903/sse"
    }

    for name, url in servers.items():
        try:
            async with sse_client(url, timeout=5.0) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    tools = await session.list_tools()
                    print(f"✅ {name}: {len(tools.tools)} 个工具可用")
        except Exception as e:
            print(f"❌ {name}: 连接失败 - {e}")

# 运行测试
asyncio.run(test_connection())
```

### 步骤3: 使用独立客户端

```python
# 运行完整示例
python examples/standalone_mcp_client.py

# 运行简单示例
python examples/simple_mcp_call.py --simple

# 运行交互式示例
python examples/simple_mcp_call.py --interactive
```

## 🔧 实用工具函数

### 快速调用函数

```python
# utils/mcp_utils.py
import asyncio
from mcp import ClientSession
from mcp.client.sse import sse_client

# 预定义服务器地址
SERVERS = {
    "jupyter": "http://localhost:8900/sse",
    "generate_sql": "http://localhost:8901/sse",
    "nl2code": "http://localhost:8902/sse",
    "aisearch": "http://localhost:8903/sse",
    "generate_ad": "http://localhost:8904/sse"
}

async def quick_call(server: str, tool: str, **kwargs):
    """快速调用MCP工具"""
    url = SERVERS.get(server)
    if not url:
        raise ValueError(f"未知服务器: {server}")

    async with sse_client(url) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            return await session.call_tool(tool, kwargs)

# 便捷函数
async def run_code(code: str, packages: list = None):
    """执行Python代码"""
    args = {"code": code}
    if packages:
        args["required_packages"] = packages
    return await quick_call("jupyter", "execute_code", **args)

async def generate_sql(question: str, db_info: str, engine: str = "dlc"):
    """生成SQL"""
    return await quick_call("generate_sql", "generate_sql",
                           engine_type=engine,
                           params={
                               "question": question,
                               "db_info": db_info,
                               "app_id": "client_app"
                           })

async def generate_code(instruction: str, scenario: str = "general"):
    """生成代码"""
    return await quick_call("nl2code", "nl2code",
                           params={
                               "user_instruction": instruction,
                               "scenario": scenario,
                               "model_name": "DeepSeek-V3-0324"
                           })

async def search_docs(question: str, num: int = 5):
    """搜索文档"""
    return await quick_call("aisearch", "aisearch_retrieve",
                           question=question, recall_num=num)

# 同步版本
def run_code_sync(code: str, packages: list = None):
    """同步执行代码"""
    return asyncio.run(run_code(code, packages))

def generate_sql_sync(question: str, db_info: str, engine: str = "dlc"):
    """同步生成SQL"""
    return asyncio.run(generate_sql(question, db_info, engine))

# 使用示例
if __name__ == "__main__":
    # 异步使用
    async def demo():
        result = await run_code("print('Hello from utils!')")
        print(result)

    asyncio.run(demo())

    # 同步使用
    result = run_code_sync("print('Hello from sync utils!')")
    print(result)
```

## 📱 命令行工具

创建一个命令行工具来快速调用MCP：

```python
# cli/mcp_cli.py
#!/usr/bin/env python3
"""MCP命令行工具"""

import asyncio
import argparse
import json
import sys
from utils.mcp_utils import quick_call, SERVERS

async def cli_main():
    """命令行主函数"""

    parser = argparse.ArgumentParser(description="MCP命令行工具")
    parser.add_argument("server", choices=SERVERS.keys(), help="服务器名称")
    parser.add_argument("tool", help="工具名称")
    parser.add_argument("--args", help="工具参数 (JSON格式)")
    parser.add_argument("--file", help="从文件读取参数")

    args = parser.parse_args()

    # 解析参数
    if args.file:
        with open(args.file, 'r') as f:
            tool_args = json.load(f)
    elif args.args:
        tool_args = json.loads(args.args)
    else:
        tool_args = {}

    try:
        # 调用工具
        result = await quick_call(args.server, args.tool, **tool_args)

        # 输出结果
        print(json.dumps(result, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(cli_main())
```

使用命令行工具：

```bash
# 执行代码
python cli/mcp_cli.py jupyter execute_code --args '{"code": "print(\"Hello CLI!\")"}'

# 生成SQL
python cli/mcp_cli.py generate_sql generate_sql --file sql_params.json

# 搜索文档
python cli/mcp_cli.py aisearch aisearch_retrieve --args '{"question": "Python教程", "recall_num": 3}'
```

## 🚀 一键启动脚本

```bash
#!/bin/bash
# quick_start.sh

echo "🚀 MCP系统一键启动"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
python3 -c "import mcp" 2>/dev/null || {
    echo "❌ MCP包未安装，请运行: pip install mcp"
    exit 1
}

# 启动服务器
echo "🚀 启动MCP服务器..."
python3 scripts/start_mcp_servers.py --test &
SERVER_PID=$!

# 等待启动
sleep 10

# 运行示例
echo "🧪 运行客户端示例..."
python3 examples/simple_mcp_call.py --simple

# 清理
echo "🧹 清理进程..."
kill $SERVER_PID 2>/dev/null

echo "✅ 演示完成"
```

## 📋 故障排除清单

### 常见问题及解决方案

1. **连接被拒绝**
   ```bash
   # 检查服务器是否启动
   netstat -tuln | grep 8900

   # 检查进程
   ps aux | grep mcp
   ```

2. **工具不存在**
   ```python
   # 检查工具列表
   async with sse_client(url) as (read, write):
       async with ClientSession(read, write) as session:
           await session.initialize()
           tools = await session.list_tools()
           print([tool.name for tool in tools.tools])
   ```

3. **参数错误**
   ```python
   # 检查工具签名
   async with sse_client(url) as (read, write):
       async with ClientSession(read, write) as session:
           await session.initialize()
           tools = await session.list_tools()
           for tool in tools.tools:
               if tool.name == "your_tool":
                   print(tool.inputSchema)
   ```

4. **超时错误**
   ```python
   # 增加超时时间
   async with sse_client(url, sse_read_timeout=600.0) as (read, write):
       # ... 调用代码
   ```

---

*MCP客户端指南版本: v1.0*
*最后更新: 2025-08-14*
*维护团队: Intellix MCP Client Team*
